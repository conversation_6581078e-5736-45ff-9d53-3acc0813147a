Collections:
  - Name: Matching-Net
    Metadata:
      Training Data: Tiered-ImageNet
      Training Techniques:
        - Adam
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/1606.04080
    README: configs/classification/matching_net/README.md

Models:
  - Name: matching-net_conv4_1xb105_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Matching-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 48.20
    Weights: https://download.openmmlab.com/mmfewshot/classification/matching_net/tiered_imagenet/matching-net_conv4_1xb105_tiered-imagenet_5way-1shot_20211120_100611-e70e9548.pth
    Config: configs/classification/matching_net/tiered_imagenet/matching-net_conv4_1xb105_tiered-imagenet_5way-1shot.py
  - Name: matching-net_conv4_1xb105_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Matching-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 61.19
    Weights: https://download.openmmlab.com/mmfewshot/classification/matching_net/tiered_imagenet/matching-net_conv4_1xb105_tiered-imagenet_5way-1shot_20211120_100611-e70e9548.pth
    Config: configs/classification/matching_net/tiered_imagenet/matching-net_conv4_1xb105_tiered-imagenet_5way-5shot.py
  - Name: matching-net_resnet12_1xb105_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Matching-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 58.97
    Weights: https://download.openmmlab.com/mmfewshot/classification/matching_net/tiered_imagenet/matching-net_resnet12_1xb105_tiered-imagenet_5way-1shot_20211120_100611-90c3124c.pth
    Config: configs/classification/matching_net/tiered_imagenet/matching-net_resnet12_1xb105_tiered-imagenet_5way-1shot.py
  - Name: matching-net_resnet12_1xb105_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Matching-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 72.1
    Weights: https://download.openmmlab.com/mmfewshot/classification/matching_net/tiered_imagenet/matching-net_resnet12_1xb105_tiered-imagenet_5way-1shot_20211120_100611-90c3124c.pth
    Config: configs/classification/matching_net/tiered_imagenet/matching-net_resnet12_1xb105_tiered-imagenet_5way-5shot.py
