Collections:
  - Name: Meta-Baseline
    Metadata:
      Training Data: Tiered-ImageNet
      Training Techniques:
        - Adam
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/2003.04390
    README: configs/classification/meta_baseline/README.md

Models:
  - Name: meta-baseline_conv4_1xb100_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 100
    In Collection: Meta-Baseline
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 53.09
    Weights: https://download.openmmlab.com/mmfewshot/classification/meta_baseline/tiered_imagenet/meta-baseline_conv4_1xb100_tiered-imagenet_5way-1shot_20211120_230843-e9c196e3.pth
    Config: configs/classification/meta_baseline/tiered_imagenet/meta-baseline_conv4_1xb100_tiered-imagenet_5way-1shot.py
  - Name: meta-baseline_conv4_1xb100_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 100
    In Collection: Meta-Baseline
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 67.85
    Weights: https://download.openmmlab.com/mmfewshot/classification/meta_baseline/tiered_imagenet/meta-baseline_conv4_1xb100_tiered-imagenet_5way-1shot_20211120_230843-e9c196e3.pth
    Config: configs/classification/meta_baseline/tiered_imagenet/meta-baseline_conv4_1xb100_tiered-imagenet_5way-5shot.py
  - Name: meta-baseline_resnet12_1xb100_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 100
    In Collection: Meta-Baseline
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 65.59
    Weights: https://download.openmmlab.com/mmfewshot/classification/meta_baseline/tiered_imagenet/meta-baseline_resnet12_1xb100_tiered-imagenet_5way-1shot_20211120_230843-6f3a6e7e.pth
    Config: configs/classification/meta_baseline/tiered_imagenet/meta-baseline_resnet12_1xb100_tiered-imagenet_5way-1shot.py
  - Name: meta-baseline_resnet12_1xb100_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 100
    In Collection: Meta-Baseline
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 79.13
    Weights: https://download.openmmlab.com/mmfewshot/classification/meta_baseline/tiered_imagenet/meta-baseline_resnet12_1xb100_tiered-imagenet_5way-1shot_20211120_230843-6f3a6e7e.pth
    Config: configs/classification/meta_baseline/tiered_imagenet/meta-baseline_resnet12_1xb100_tiered-imagenet_5way-5shot.py
