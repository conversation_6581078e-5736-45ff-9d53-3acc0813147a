Collections:
  - Name: Neg-Margin
    Metadata:
      Training Data: CUB
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/2003.12060
    README: configs/classification/neg_margin/README.md

Models:
  - Name: neg-margin_cosine_conv4_1xb64_cub_5way-1shot
    Metadata:
      Training Data: CUB
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 64.08
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/cub/neg-margin_cosine_conv4_1xb64_cub_5way-1shot_20211120_100620-5415a152.pth
    Config: configs/classification/neg_margin/cub/neg-margin_cosine_conv4_1xb64_cub_5way-1shot.py
  - Name: neg-margin_cosine_conv4_1xb64_cub_5way-5shot
    Metadata:
      Training Data: CUB
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 80.69
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/cub/neg-margin_cosine_conv4_1xb64_cub_5way-1shot_20211120_100620-5415a152.pth
    Config: configs/classification/neg_margin/cub/neg-margin_cosine_conv4_1xb64_cub_5way-5shot.py
  - Name: neg-margin_cosine_resnet12_1xb64_cub_5way-1shot
    Metadata:
      Training Data: CUB
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 78.54
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/cub/neg-margin_cosine_resnet12_1xb64_cub_5way-1shot_20211120_100620-b4ab9cc1.pth
    Config: configs/classification/neg_margin/cub/neg-margin_cosine_resnet12_1xb64_cub_5way-1shot.py
  - Name: neg-margin_cosine_resnet12_1xb64_cub_5way-5shot
    Metadata:
      Training Data: CUB
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 90.19
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/cub/neg-margin_cosine_resnet12_1xb64_cub_5way-1shot_20211120_100620-b4ab9cc1.pth
    Config: configs/classification/neg_margin/cub/neg-margin_cosine_resnet12_1xb64_cub_5way-5shot.py
