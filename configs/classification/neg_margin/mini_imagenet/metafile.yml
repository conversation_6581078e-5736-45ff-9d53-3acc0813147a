Collections:
  - Name: Neg-Margin
    Metadata:
      Training Data: Mini-ImageNet
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/2003.12060
    README: configs/classification/neg_margin/README.md

Models:
  - Name: neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-1shot
    Metadata:
      Training Data: Mini-ImageNet
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: Mini-ImageNet
        Metrics:
          Accuracy: 51.15
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/mini_imagenet/neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-1shot_20211120_104933-8a1340d3.pth
    Config: configs/classification/neg_margin/mini_imagenet/neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-1shot.py
  - Name: neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-5shot
    Metadata:
      Training Data: Mini-ImageNet
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: Mini-ImageNet
        Metrics:
          Accuracy: 67.32
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/mini_imagenet/neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-1shot_20211120_104933-8a1340d3.pth
    Config: configs/classification/neg_margin/mini_imagenet/neg-margin_cosine_conv4_1xb64_mini-imagenet_5way-5shot.py
  - Name: neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-1shot
    Metadata:
      Training Data: Mini-ImageNet
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: Mini-ImageNet
        Metrics:
          Accuracy: 61.7
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/mini_imagenet/neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-1shot_20211120_110018-e3aae9b5.pth
    Config: configs/classification/neg_margin/mini_imagenet/neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-1shot.py
  - Name: neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-5shot
    Metadata:
      Training Data: Mini-ImageNet
      Epochs: 200
      Batch Size: 64
    In Collection: Neg-Margin
    Results:
      - Task: Few Shot Image Classification
        Dataset: Mini-ImageNet
        Metrics:
          Accuracy: 78.03
    Weights: https://download.openmmlab.com/mmfewshot/classification/neg_margin/mini_imagenet/neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-1shot_20211120_110018-e3aae9b5.pth
    Config: configs/classification/neg_margin/mini_imagenet/neg-margin_cosine_resnet12_1xb64_mini-imagenet_5way-5shot.py
