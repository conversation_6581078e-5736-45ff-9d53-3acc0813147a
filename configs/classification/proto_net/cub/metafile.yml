Collections:
  - Name: Proto-Net
    Metadata:
      Training Data: CUB
      Training Techniques:
        - Adam
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/1703.05175
    README: configs/classification/proto_net/README.md

Models:
  - Name: proto-net_conv4_1xb105_cub_5way-1shot
    Metadata:
      Training Data: CUB
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 58.86
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/cub/proto-net_conv4_1xb105_cub_5way-1shot_20211120_101211-9ab530c3.pth
    Config: configs/classification/proto_net/cub/proto-net_conv4_1xb105_cub_5way-1shot.py
  - Name: proto-net_conv4_1xb105_cub_5way-5shot
    Metadata:
      Training Data: CUB
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 80.77
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/cub/proto-net_conv4_1xb105_cub_5way-1shot_20211120_101211-9ab530c3.pth
    Config: configs/classification/proto_net/cub/proto-net_conv4_1xb105_cub_5way-5shot.py
  - Name: proto-net_resnet12_1xb105_cub_5way-1shot
    Metadata:
      Training Data: CUB
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 74.35
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/cub/proto-net_resnet12_1xb105_cub_5way-1shot_20211120_101211-da5bfb99.pth
    Config: configs/classification/proto_net/cub/proto-net_resnet12_1xb105_cub_5way-1shot.py
  - Name: proto-net_resnet12_1xb105_cub_5way-5shot
    Metadata:
      Training Data: CUB
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: CUB
        Metrics:
          Accuracy: 88.5
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/cub/proto-net_resnet12_1xb105_cub_5way-1shot_20211120_101211-da5bfb99.pth
    Config: configs/classification/proto_net/cub/proto-net_resnet12_1xb105_cub_5way-5shot.py
