Collections:
  - Name: Proto-Net
    Metadata:
      Training Data: Tiered-ImageNet
      Training Techniques:
        - Adam
      Training Resources: 1x V100 GPUs
    Paper: https://arxiv.org/abs/1703.05175
    README: configs/classification/proto_net/README.md

Models:
  - Name: proto-net_conv4_1xb105_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 45.5
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/tiered_imagenet/proto-net_conv4_1xb105_tiered-imagenet_5way-1shot_20211120_134742-26520ca8.pth
    Config: configs/classification/proto_net/tiered_imagenet/proto-net_conv4_1xb105_tiered-imagenet_5way-1shot.py
  - Name: proto-net_conv4_1xb105_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 62.89
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/tiered_imagenet/proto-net_conv4_1xb105_tiered-imagenet_5way-1shot_20211120_134742-26520ca8.pth
    Config: configs/classification/proto_net/tiered_imagenet/proto-net_conv4_1xb105_tiered-imagenet_5way-5shot.py
  - Name: proto-net_resnet12_1xb105_tiered-imagenet_5way-1shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 59.11
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/tiered_imagenet/proto-net_resnet12_1xb105_tiered-imagenet_5way-1shot_20211120_153230-eb72884e.pth
    Config: configs/classification/proto_net/tiered_imagenet/proto-net_resnet12_1xb105_tiered-imagenet_5way-1shot.py
  - Name: proto-net_resnet12_1xb105_tiered-imagenet_5way-5shot
    Metadata:
      Training Data: Tiered-ImageNet
      Batch Size: 105
    In Collection: Proto-Net
    Results:
      - Task: Few Shot Image Classification
        Dataset: Tiered-ImageNet
        Metrics:
          Accuracy: 75.3
    Weights: https://download.openmmlab.com/mmfewshot/classification/proto_net/tiered_imagenet/proto-net_resnet12_1xb105_tiered-imagenet_5way-1shot_20211120_153230-eb72884e.pth
    Config: configs/classification/proto_net/tiered_imagenet/proto-net_resnet12_1xb105_tiered-imagenet_5way-5shot.py
