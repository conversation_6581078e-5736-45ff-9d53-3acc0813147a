Collections:
  - Name: Attention RPN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 4x V100 GPUs
      Architecture:
        - Attention RPN
        - ResNet
    Paper: https://arxiv.org/abs/1908.01998
    README: configs/detection/attention_rpn/README.md

Models:
  - Name: attention_rpn_r50_c4_coco_10shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_10shot-fine-tuning.py
    Metadata:
      Training Data: COCO 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          novel box AP: 9.2
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_10shot-fine-tuning_20211103_003801-94ec8ada.pth
  - Name: attention_rpn_r50_c4_coco_30shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_30shot-fine-tuning.py
    Metadata:
      Training Data: COCO 30shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          novel box AP: 14.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_30shot-fine-tuning_20211103_010800-50611991.pth
  - Name: attention_rpn_r50_c4_coco_base-training
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_base-training.py
    Metadata:
      Training Data: COCO Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 23.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_base-training_20211102_003348-da28cdfd.pth
  - Name: attention_rpn_r50_c4_coco_official-10shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_official-10shot-fine-tuning.py
    Metadata:
      Training Data: COCO 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          novel box AP: 11.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_official-10shot-fine-tuning_20211107_214729-6d046301.pth
  - Name: attention_rpn_r50_c4_coco_official-base-training
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_official-base-training.py
    Metadata:
      Training Data: COCO Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 24.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/coco/attention-rpn_r50_c4_4xb2_coco_official-base-training_20211102_003347-f9e2dab0.pth
