Collections:
  - Name: Attention RPN
    Metadata:
      Training Data: VOC Split3
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Attention RPN
        - ResNet
    Paper: https://arxiv.org/abs/1908.01998
    README: configs/detection/attention_rpn/README.md

Models:
  - Name: attention-rpn_r50_c4_voc-split3_base-training
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_base-training.py
    Metadata:
      Training Data: VOC Split3 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          base box AP50: 73.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_base-training_20211101_073701-5672bea8.pth
  - Name: attention_rpn_r50_c4_voc-split3_1shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          novel box AP50: 31.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_1shot-fine-tuning_20211102_022503-b47a5610.pth
  - Name: attention_rpn_r50_c4_voc-split3_2shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          novel box AP50: 30.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_2shot-fine-tuning_20211102_025331-7a4d4e9b.pth
  - Name: attention_rpn_r50_c4_voc-split3_3shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          novel box AP50: 38.2
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_3shot-fine-tuning_20211102_032300-6a3c6fb4.pth
  - Name: attention_rpn_r50_c4_voc-split3_5shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          novel box AP50: 48.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_5shot-fine-tuning_20211102_035311-1420872c.pth
  - Name: attention_rpn_r50_c4_voc-split3_10shot-fine-tuning
    In Collection: Attention RPN
    Config: configs/detection/attention_rpn/coco/attention-rpn_r50_c4_voc-split3_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC
        Metrics:
          novel box AP50:  51.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/attention_rpn/voc/split3/attention-rpn_r50_c4_voc-split3_10shot-fine-tuning_20211102_042423-6724602a.pth
