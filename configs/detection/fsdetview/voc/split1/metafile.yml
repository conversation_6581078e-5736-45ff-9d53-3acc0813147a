Collections:
  - Name: FSDETVIEW
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Batch Size: 8x4
      Architecture:
        - RPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.12107
    README: configs/detection/fsdetview/README.md

Models:
  - Name: fsdetview_r101_c4_8xb4_voc-split1_base-training
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_base-training.py
    Metadata:
      Training Data: VOC Split1 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 74.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_base-training_20211101_104321-6890f5da.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split1_1shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 61.1
          novel box AP: 35.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_1shot-fine-tuning_20211111_174458-7f003e09.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split1_2shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 67.9
          novel box AP: 49.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_2shot-fine-tuning_20211111_175533-6a218bf8.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split1_3shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 68.1
          novel box AP: 54.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_3shot-fine-tuning_20211111_181021-7fed633f.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split1_5shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 69.9
          novel box AP: 60.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_5shot-fine-tuning_20211111_183331-2bad7372.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split1_10shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 71.7
          novel box AP: 61.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split1/fsdetview_r101_c4_8xb4_voc-split1_10shot-fine-tuning_20211111_185540-3717717b.pth
