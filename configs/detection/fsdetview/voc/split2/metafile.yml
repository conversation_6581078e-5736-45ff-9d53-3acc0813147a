Collections:
  - Name: FSDETVIEW
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Batch Size: 8x4
      Architecture:
        - RPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.12107
    README: configs/detection/fsdetview/README.md

Models:
  - Name: fsdetview_r101_c4_8xb4_voc-split2_base-training
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_base-training.py
    Metadata:
      Training Data: VOC Split2 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 74.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_base-training_20211101_104321-6890f5da.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split2_1shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 65.0
          novel box AP: 27.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_1shot-fine-tuning_20211111_192244-0fb62181.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split2_2shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 69.6
          novel box AP: 36.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_2shot-fine-tuning_20211111_193302-77a3e0ed.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split2_3shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 70.9
          novel box AP: 41.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_3shot-fine-tuning_20211111_194805-a9746764.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split2_5shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 71.3
          novel box AP: 43.2
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_5shot-fine-tuning_20211111_201121-627d8bab.pth
  - Name: fsdetview_r101_c4_8xb4_voc-split2_10shot-fine-tuning
    In Collection: FSDETVIEW
    Config: configs/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 72.4
          novel box AP: 47.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/fsdetview/voc/split2/fsdetview_r101_c4_8xb4_voc-split2_10shot-fine-tuning_20211111_203317-1d8371fa.pth
