Collections:
  - Name: Meta RCNN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Batch Size: 8x4
      Architecture:
        - RPN
        - ResNet
    Paper: https://arxiv.org/abs/1909.13032
    README: configs/detection/meta_rcnn/README.md

Models:
  - Name: meta-rcnn_r50_c4_8xb4_coco_base-training
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_base-training.py
    Metadata:
      Training Data: COCO Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 27.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_base-training_20211102_213915-65a22539.pth
  - Name: meta-rcnn_r50_c4_8xb4_coco_10shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_10shot-fine-tuning.py
    Metadata:
      Training Data: COCO 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 25.1
          novel box AP: 9.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_10shot-fine-tuning_20211112_090638-e703f762.pth
  - Name: meta-rcnn_r50_c4_8xb4_coco_30shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_30shot-fine-tuning.py
    Metadata:
      Training Data: COCO 30shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 26.9
          novel box AP: 11.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/coco/meta-rcnn_r50_c4_8xb4_coco_30shot-fine-tuning_20211112_110452-50d791dd.pth
