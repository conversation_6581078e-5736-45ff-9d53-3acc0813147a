Collections:
  - Name: Meta RCNN
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Batch Size: 8x4
      Architecture:
        - RPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.12107
    README: configs/detection/meta_rcnn/README.md

Models:
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_base-training
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_base-training.py
    Metadata:
      Training Data: VOC Split1 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 72.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_base-training_20211101_234042-7184a596.pth
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_1shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 58.8
          novel box AP: 40.2
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_1shot-fine-tuning_20211111_173217-b872c72a.pth
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_2shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 67.7
          novel box AP: 49.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_2shot-fine-tuning_20211111_173941-75b01b1d.pth
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_3shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 69.0
          novel box AP: 54.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_3shot-fine-tuning_20211111_175026-6b556b8c.pth
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_5shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 70.8
          novel box AP: 55.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_5shot-fine-tuning_20211111_180727-d9194139.pth
  - Name: meta-rcnn_r101_c4_8xb4_voc-split1_10shot-fine-tuning
    In Collection: Meta RCNN
    Config: configs/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 71.7
          novel box AP: 56.3
    Weights: https://download.openmmlab.com/mmfewshot/detection/meta_rcnn/voc/split1/meta-rcnn_r101_c4_8xb4_voc-split1_10shot-fine-tuning_20211111_182413-f3db91b6.pth
