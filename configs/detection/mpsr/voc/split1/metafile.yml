Collections:
  - Name: MPSR
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 2x V100 GPUs
      Batch Size: 2x2
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.09384
    README: configs/detection/mpsr/README.md

Models:
  - Name: mpsr_r101_fpn_2xb2_voc-split1_base-training
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_base-training.py
    Metadata:
      Training Data: VOC Split1 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 80.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_base-training_20211107_135130-ea747c7b.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split1_1shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 60.6
          novel box AP: 38.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_1shot-fine-tuning_20211109_130330-444b743a.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split1_2shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 65.9
          novel box AP: 45.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_2shot-fine-tuning_20211109_130330-3a778216.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split1_3shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 68.1
          novel box AP: 49.2
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_3shot-fine-tuning_20211109_130347-f5baa2f7.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split1_5shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 69.2
          novel box AP: 55.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_5shot-fine-tuning_20211109_130347-620065e8.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split1_10shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 71.2
          novel box AP: 58.7
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split1/mpsr_r101_fpn_2xb2_voc-split1_10shot-fine-tuning_20211109_130430-d87b3b4b.pth
