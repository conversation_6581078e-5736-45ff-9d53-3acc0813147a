Collections:
  - Name: MPSR
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 2x V100 GPUs
      Batch Size: 2x2
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.09384
    README: configs/detection/mpsr/README.md

Models:
  - Name: mpsr_r101_fpn_2xb2_voc-split2_base-training
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_base-training.py
    Metadata:
      Training Data: VOC Split2 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 81.3
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_base-training_20211107_135130-c7b4ee3f.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split2_1shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 61.0
          novel box AP: 25.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_1shot-fine-tuning_20211107_195800-48163ea0.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split2_2shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 66.9
          novel box AP: 29.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_2shot-fine-tuning_20211107_203755-65afa20b.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split2_3shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 67.6
          novel box AP: 40.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_3shot-fine-tuning_20211107_110120-832962b1.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split2_5shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 70.4
          novel box AP: 41.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_5shot-fine-tuning_20211107_114449-ea834f31.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split2_10shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 71.7
          novel box AP: 47.1
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split2/mpsr_r101_fpn_2xb2_voc-split2_10shot-fine-tuning_20211107_122815-8108834b.pth
