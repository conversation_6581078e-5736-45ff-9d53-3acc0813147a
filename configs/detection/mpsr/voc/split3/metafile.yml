Collections:
  - Name: MPSR
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 2x V100 GPUs
      Batch Size: 2x2
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2007.09384
    README: configs/detection/mpsr/README.md

Models:
  - Name: mpsr_r101_fpn_2xb2_voc-split3_base-training
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_base-training.py
    Metadata:
      Training Data: VOC Split3 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 81.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_base-training_20211107_135304-3528e346.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split3_1shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 57.9
          novel box AP: 34.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_1shot-fine-tuning_20211107_131308-c0e1d1f0.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split3_2shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 65.7
          novel box AP: 41.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_2shot-fine-tuning_20211107_135527-70053e26.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split3_3shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 69.1
          novel box AP: 44.1
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_3shot-fine-tuning_20211107_155433-8955b1d3.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split3_5shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 70.4
          novel box AP: 48.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_5shot-fine-tuning_20211107_171449-a9931117.pth
  - Name: mpsr_r101_fpn_2xb2_voc-split3_10shot-fine-tuning
    In Collection: MPSR
    Config: configs/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 72.5
          novel box AP: 51.7
    Weights: https://download.openmmlab.com/mmfewshot/detection/mpsr/voc/split3/mpsr_r101_fpn_2xb2_voc-split3_10shot-fine-tuning_20211107_183534-698b6503.pth
