Collections:
  - Name: TFA
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2003.06957
    README: configs/detection/tfa/README.md

Models:
  - Name: tfa_r101_fpn_coco_base-training
    In Collection: TFA
    Config: configs/detection/tfa/coco/tfa_r101_fpn_coco_base-training.py
    Metadata:
      Training Data: COCO Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 39.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/coco/tfa_r101_fpn_coco_base-training_20211102_030413-a67975c7.pth
  - Name: tfa_r101_fpn_coco_10shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/coco/tfa_r101_fpn_coco_10shot-fine-tuning.py
    Metadata:
      Training Data: COCO 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 35.2
          novel box AP: 10.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/coco/tfa_r101_fpn_coco_10shot-fine-tuning_20211102_162241-8abd2a82.pth
  - Name: tfa_r101_fpn_coco_30shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/coco/tfa_r101_fpn_coco_30shot-fine-tuning.py
    Metadata:
      Training Data: COCO 30shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: COCO
        Metrics:
          base box AP: 36.7
          novel box AP: 14.7
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/coco/tfa_r101_fpn_coco_30shot-fine-tuning_20211103_001731-a63fce47.pth
