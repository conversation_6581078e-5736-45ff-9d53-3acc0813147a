Collections:
  - Name: TFA
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2003.06957
    README: configs/detection/tfa/README.md

Models:
  - Name: tfa_r101_fpn_voc-split1_base-training
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_base-training.py
    Metadata:
      Training Data: VOC Split1 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 80.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_base-training_20211031_114821-efbd13e9.pth
  - Name: tfa_r101_fpn_voc-split1_1shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 79.2
          novel box AP: 41.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_1shot-fine-tuning_20211031_204528-9d6b2d28.pth
  - Name: tfa_r101_fpn_voc-split1_2shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 79.2
          novel box AP: 49.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_2shot-fine-tuning_20211101_003504-d5083628.pth
  - Name: tfa_r101_fpn_voc-split1_3shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 79.6
          novel box AP: 49.9
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_3shot-fine-tuning_20211101_005934-10ad61cd.pth
  - Name: tfa_r101_fpn_voc-split1_5shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 79.6
          novel box AP: 58.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_5shot-fine-tuning_20211101_013516-5d682ebb.pth
  - Name: tfa_r101_fpn_voc-split1_10shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split1 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: 79.7
          novel box AP: 58.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split1/tfa_r101_fpn_voc-split1_10shot-fine-tuning_20211101_023154-1f3d1ff1.pth
