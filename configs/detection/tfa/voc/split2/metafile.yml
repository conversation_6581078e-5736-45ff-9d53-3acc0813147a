Collections:
  - Name: TFA
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2003.06957
    README: configs/detection/tfa/README.md

Models:
  - Name: tfa_r101_fpn_voc-split2_base-training
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_base-training.py
    Metadata:
      Training Data: VOC Split2 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 82.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_base-training_20211031_114820-d47f8ef9.pth
  - Name: tfa_r101_fpn_voc-split2_1shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 80.3
          novel box AP: 26.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_1shot-fine-tuning_20211031_222829-a476e97f.pth
  - Name: tfa_r101_fpn_voc-split2_2shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 78.1
          novel box AP: 30.7
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_2shot-fine-tuning_20211101_042109-eb35e87e.pth
  - Name: tfa_r101_fpn_voc-split2_3shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 79.4
          novel box AP: 39.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_3shot-fine-tuning_20211101_044601-db0cd2b3.pth
  - Name: tfa_r101_fpn_voc-split2_5shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 79.4
          novel box AP: 35.7
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_5shot-fine-tuning_20211101_052148-d2edde97.pth
  - Name: tfa_r101_fpn_voc-split2_10shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split2 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split2
        Metrics:
          base box AP: 79.7
          novel box AP: 40.5
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split2/tfa_r101_fpn_voc-split2_10shot-fine-tuning_20211101_061828-9c0cd7cd.pth
