Collections:
  - Name: TFA
    Metadata:
      Training Data: VOC
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - RPN
        - FPN
        - ResNet
    Paper: https://arxiv.org/abs/2003.06957
    README: configs/detection/tfa/README.md

Models:
  - Name: tfa_r101_fpn_voc-split3_base-training
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_base-training.py
    Metadata:
      Training Data: VOC Split3 Base Classes
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split1
        Metrics:
          base box AP: none
    Weights: none
  - Name: tfa_r101_fpn_voc-split3_1shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_1shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 1shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 80.5
          novel box AP: 34.0
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_1shot-fine-tuning_20211031_222908-509b00cd.pth
  - Name: tfa_r101_fpn_voc-split3_2shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_2shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 2shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 80.6
          novel box AP: 39.3
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_2shot-fine-tuning_20211101_080733-2b0bc25b.pth
  - Name: tfa_r101_fpn_voc-split3_3shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_3shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 3shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 81.1
          novel box AP: 42.8
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_3shot-fine-tuning_20211101_083226-48983379.pth
  - Name: tfa_r101_fpn_voc-split3_5shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_5shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 5shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 80.8
          novel box AP: 51.4
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_5shot-fine-tuning_20211101_090812-da47ba99.pth
  - Name: tfa_r101_fpn_voc-split3_10shot-fine-tuning
    In Collection: TFA
    Config: configs/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_10shot-fine-tuning.py
    Metadata:
      Training Data: VOC Split3 10shot
    Results:
      - Task: Few Shot Object Detection
        Dataset: VOC Split3
        Metrics:
          base box AP: 80.7
          novel box AP: 50.6
    Weights: https://download.openmmlab.com/mmfewshot/detection/tfa/voc/split3/tfa_r101_fpn_voc-split3_10shot-fine-tuning_20211101_100435-ef8d4023.pth
