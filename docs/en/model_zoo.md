# Model Zoo

## Few Shot Classification Model Zoo

#### Baseline

Please refer to [Baseline](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/baseline) for details.

#### Baseline++

Please refer to [Baseline++](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/baseline_plus) for details.

#### ProtoNet

Please refer to [ProtoNet](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/proto_net) for details.

#### RelationNet

Please refer to [RelationNet](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/relation_net) for details.

#### MatchingNet

Please refer to [MatchingNet](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/matching_net) for details.

#### MAML

Please refer to [MAML](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/maml) for details.

#### NegMargin

Please refer to [NegMargin](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/neg_margin) for details.

#### Meta Baseline

Please refer to [Meta Baseline](https://github.com/open-mmlab/mmfewshot/tree/main/configs/classification/meta_baseline) for details.

## Few Shot Detection Model Zoo

#### TFA

Please refer to [TFA](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/tfa) for details.

#### FSCE

Please refer to [FSCE](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/fsce) for details.

#### Meta RCNN

Please refer to [Meta RCNN](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/meta_rcnn) for details.

#### FSDetView

Please refer to [FSDetView](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/fsdetview) for details.

#### Attention RPN

Please refer to [Attention RPN](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/attention_rpn) for details.

#### MPSR

Please refer to [MPSR](https://github.com/open-mmlab/mmfewshot/tree/main/configs/detection/mpsr) for details.
