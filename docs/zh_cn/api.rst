mmfewshot.classification
=================


classification.apis
------------------------------
.. automodule:: mmfewshot.classification.apis
    :members:

classification.core
------------------------------
.. automodule:: mmfewshot.classification.core
    :members:

evaluation
^^^^^^^^^^
.. automodule:: mmfewshot.classification.core.evaluation
    :members:


classification.datasets
------------------------------
.. automodule:: mmfewshot.classification.datasets
    :members:


classification.models
------------------------------
.. automodule:: mmfewshot.classification.models
    :members:

backbones
^^^^^^^^^^
.. automodule:: mmfewshot.classification.models.backbones
    :members:

classifier
^^^^^^^^^^
.. automodule:: mmfewshot.classification.models.classifier
    :members:

heads
^^^^^^^^^^
.. automodule:: mmfewshot.classification.models.heads
    :members:

losses
^^^^^^^^^^
.. automodule:: mmfewshot.classification.models.losses
    :members:

utils
^^^^^^^^^^
.. automodule:: mmfewshot.classification.models.utils
    :members:


classification.utils
------------------------------
.. automodule:: mmfewshot.classification.utils
    :members:


mmfewshot.detection
=================

detection.apis
------------------------------
.. automodule:: mmfewshot.detection.apis
    :members:


detection.core
------------------------------
.. automodule:: mmfewshot.detection.core
    :members:

evaluation
^^^^^^^^^^
.. automodule:: mmfewshot.detection.core.evaluation
    :members:

utils
^^^^^^^^^^
.. automodule:: mmfewshot.detection.core.utils
    :members:

detection.datasets
------------------------------
.. automodule:: mmfewshot.detection.datasets
    :members:


detection.models
------------------------------
.. automodule:: mmfewshot.detection.models
    :members:

backbones
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.backbones
    :members:

dense_heads
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.dense_heads
    :members:

detectors
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.detectors
    :members:

losses
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.losses
    :members:

roi_heads
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.roi_heads
    :members:

utils
^^^^^^^^^^
.. automodule:: mmfewshot.detection.models.utils
    :members:


detection.utils
------------------------------
.. automodule:: mmfewshot.detection.utils
    :members:


mmfewshot.utils
=================
.. automodule:: mmfewshot.utils
    :members:
