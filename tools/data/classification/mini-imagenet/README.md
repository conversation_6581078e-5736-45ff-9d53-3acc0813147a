# Preparing Mini-ImageNet Dataset

<!-- [DATASET] -->

```bibtex
@inproceedings{ren18fewshotssl,
    author = {<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},
    title = {Meta-Learning for Semi-Supervised Few-Shot Classification},
    booktitle = {Proceedings of 6th International Conference on Learning Representations {ICLR}},
    year = {2018},
}

@article{ILSVRC15,
    Author = {<PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON>v <PERSON> and <PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON>},
    Title = {{ImageNet Large Scale Visual Recognition Challenge}},
    Year = {2015},
    journal = {International Journal of Computer Vision (IJCV)},
    doi = {10.1007/s11263-015-0816-y},
    volume = {115},
    number = {3},
    pages = {211-252}
}
```

The split files of mini-imagenet can be downloaded from [here](https://github.com/twitter-research/meta-learning-lstm/tree/master/data/miniImagenet).
The whole imagenet dataset can be downloaded from [here](https://image-net.org/challenges/LSVRC/2012/index.php).

The data structure is as follows:

```text
mmfewshot
├── mmfewshot
├── configs
├── data
│   ├── mini_imagenet
│   │   ├── images
│   │   │   ├── n01440764
│   │   │   │   ├── n01440764_10026.JPEG
│   │   │   │   ├── ...
│   │   │   ├── n01443537
│   │   │   ├── ...
│   │   ├── test.csv
│   │   ├── train.csv
│   │   ├── val.csv
...
```
